// Zustand auth store
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, LoginResponse } from '../types/auth.types';

interface AuthStore extends AuthState {
  // Actions
  login: (loginData: LoginResponse) => void;
  logout: () => void;
  setToken: (token: string) => void;
  clearAuth: () => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      // Initial state
      isAuthenticated: false,
      token: null,
      employee: null,
      session: null,

      // Actions
      login: (loginData: LoginResponse) => {
        set({
          isAuthenticated: true,
          token: loginData.token,
          employee: loginData.employee,
          session: loginData.session,
        });
      },

      logout: () => {
        set({
          isAuthenticated: false,
          token: null,
          employee: null,
          session: null,
        });
      },

      setToken: (token: string) => {
        set({ token });
      },

      clearAuth: () => {
        set({
          isAuthenticated: false,
          token: null,
          employee: null,
          session: null,
        });
      },
    }),
    {
      name: 'auth-storage', // localStorage key
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        token: state.token,
        employee: state.employee,
        session: state.session,
      }),
    }
  )
);
