// Authentication service - Business logic
import { PrismaClient } from '../generated/client';
import { HashUtils } from '../utils/hash.utils';
import { JwtUtils } from '../utils/jwt.utils';
import { ErrorHandler } from '../middlewares/error.middleware';
import { 
  PinLoginRequest, 
  PinLoginResponse, 
  EmployeeInfo, 
  SessionInfo 
} from '../types/auth.types';

export class AuthService {
  constructor(private prisma: PrismaClient) {}

  /**
   * PIN ile giriş yapar
   */
  async pinLogin(request: PinLoginRequest, deviceInfo?: string): Promise<PinLoginResponse> {
    const { employeeId, pin } = request;

    // Çalışanı bul
    const employee = await this.prisma.user.findUnique({
      where: { id: employeeId },
      include: {
        branch: true,
      },
    });

    if (!employee) {
      throw ErrorHandler.createError('Çalışan bulunamadı', 404, 'EMPLOYEE_NOT_FOUND');
    }

    if (!employee.active) {
      throw ErrorHandler.createError('Çalışan hesabı aktif değil', 401, 'EMPLOYEE_INACTIVE');
    }

    if (!employee.pin) {
      throw ErrorHandler.createError('Çalışan PIN\'i tanımlanmamış', 401, 'PIN_NOT_SET');
    }

    // PIN doğrulama
    const isPinValid = await HashUtils.verifyPin(pin, employee.pin);
    if (!isPinValid) {
      throw ErrorHandler.createError('Geçersiz PIN', 401, 'INVALID_PIN');
    }

    // Eski session'ları sonlandır
    await this.prisma.session.updateMany({
      where: {
        userId: employee.id,
        endedAt: null,
      },
      data: {
        endedAt: new Date(),
      },
    });

    // Yeni session oluştur
    const session = await this.prisma.session.create({
      data: {
        userId: employee.id,
        branchId: employee.branchId || '',
        token: '', // Token'ı aşağıda güncelleyeceğiz
        deviceInfo,
        startedAt: new Date(),
        lastActivityAt: new Date(),
      },
    });

    // JWT token oluştur
    const token = JwtUtils.generateToken({
      userId: employee.id,
      sessionId: session.id,
      branchId: employee.branchId || '',
      role: employee.role,
    });

    // Session'ı token ile güncelle
    await this.prisma.session.update({
      where: { id: session.id },
      data: { token },
    });

    // Son giriş zamanını güncelle
    await this.prisma.user.update({
      where: { id: employee.id },
      data: { lastLoginAt: new Date() },
    });

    // Response hazırla
    const employeeInfo: EmployeeInfo = {
      id: employee.id,
      firstName: employee.firstName,
      lastName: employee.lastName,
      role: employee.role,
      branchId: employee.branchId,
      email: employee.email,
      phone: employee.phone,
    };

    const sessionInfo: SessionInfo = {
      id: session.id,
      startedAt: session.startedAt,
      branchId: session.branchId,
    };

    return {
      token,
      employee: employeeInfo,
      session: sessionInfo,
    };
  }

  /**
   * Session'ı sonlandırır (logout)
   */
  async logout(sessionId: string): Promise<void> {
    await this.prisma.session.update({
      where: { id: sessionId },
      data: { endedAt: new Date() },
    });
  }

  /**
   * Session'ı doğrular ve günceller
   */
  async validateSession(sessionId: string): Promise<boolean> {
    const session = await this.prisma.session.findUnique({
      where: { id: sessionId },
      include: { user: true },
    });

    if (!session || session.endedAt) {
      return false;
    }

    // Son aktivite zamanını güncelle
    await this.prisma.session.update({
      where: { id: sessionId },
      data: { lastActivityAt: new Date() },
    });

    return true;
  }
}
