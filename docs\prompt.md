<PERSON>ral, al sana **DÜZELTİLMİŞ** prompt şablonları:

## 🔧 BACKEND PROMPT

```markdown
## Backend: [FEATURE ADI] API

### İhtiyaç
[Ne için kullanılacak - örn: PIN ile kullanıcı girişi]

### Özellikler
- [Özellik 1]
- [Özellik 2]

### Teknik Gereksinimler
- Express + TypeScript
- Prisma ORM (schema hazır)
- Zod validation
- JWT authentication
- Proper error handling (try-catch + middleware)
- HTTP status codes kullan

### Notlar
- [Varsa özel durum]
```

## 🎨 FRONTEND PROMPT

```markdown
## Frontend: [FEATURE ADI] Sayfası

### Tasarım
[Screenshot varsa ekle]

### Özellikler
- [UI özellik 1]
- [UI özellik 2]

### Backend API
[Hangi endpoint'ler var - örn: /api/auth/pin-login]

### Teknik Gereksinimler
- React + TypeScript
- Tailwind CSS
- React Hook Form + Zod validation
- Zustand state management
- Axios + interceptors
- Loading states (spinner/skeleton)
- Error handling (toast notifications)

### Notlar
- Türkçe UI
- [Varsa özel durum]
```

**ARTIK HER ŞEY TAMAM!** Copy-paste yap, kullan! 🚀