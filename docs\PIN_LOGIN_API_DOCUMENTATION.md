# 🔧 ATORPOS PIN GİRİŞİ API DOKÜMANTASYONU

## 📋 Genel Bilgiler

- **Base URL**: `http://localhost:3001`
- **API Version**: `v1`
- **Content-Type**: `application/json`
- **Authentication**: JWT Bearer Token

## 🔐 Authentication Endpoints

### 1. PIN ile Giriş
**POST** `/api/v1/auth/pin-login`

Çalışanların PIN ile sisteme giriş yapmasını sağlar.

**Request Body:**
```json
{
  "employeeId": "string",
  "pin": "string" // 4-6 haneli sayı
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "<PERSON><PERSON><PERSON> başarılı",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "employee": {
      "id": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "BRANCH_MANAGER|CASHIER|WAITER|KITCHEN",
      "branchId": "string",
      "email": "string|null",
      "phone": "string|null"
    },
    "session": {
      "id": "string",
      "startedAt": "2024-01-01T00:00:00.000Z",
      "branchId": "string"
    }
  }
}
```

**Error Responses:**
- `400`: Validation hatası
- `401`: Geçersiz PIN
- `404`: Çalışan bulunamadı
- `429`: Rate limit aşıldı (5 deneme/10 dakika)

**Rate Limiting:**
- 5 hatalı deneme sonrası 10 dakika bekleme
- IP + employeeId bazlı

### 2. Çıkış
**POST** `/api/v1/auth/logout`

Çalışanın oturumunu sonlandırır.

**Headers:**
```
Authorization: Bearer <token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Çıkış başarılı"
}
```

### 3. Token Doğrulama
**GET** `/api/v1/auth/verify`

JWT token'ın geçerliliğini kontrol eder.

**Headers:**
```
Authorization: Bearer <token>
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Token geçerli",
  "data": {
    "userId": "string",
    "role": "string",
    "branchId": "string",
    "sessionId": "string"
  }
}
```

### 4. Kullanıcı Profili
**GET** `/api/v1/auth/profile`

Giriş yapmış kullanıcının profil bilgilerini getirir.

**Headers:**
```
Authorization: Bearer <token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string|null",
    "phone": "string|null",
    "role": "string",
    "branchId": "string",
    "lastLoginAt": "2024-01-01T00:00:00.000Z",
    "branch": {
      "id": "string",
      "name": "string",
      "code": "string"
    }
  }
}
```

## 👥 Employee Endpoints

### 1. Aktif Vardiyası Olan Çalışanlar
**GET** `/api/v1/employees/active-shift`

Giriş ekranında gösterilecek aktif çalışanları getirir.

**Query Parameters:**
- `branchId` (optional): Şube filtresi

**Success Response (200):**
```json
{
  "success": true,
  "message": "Aktif çalışanlar getirildi",
  "data": [
    {
      "id": "string",
      "firstName": "string",
      "lastName": "string",
      "role": "string",
      "branchId": "string|null",
      "profileImage": "string|null"
    }
  ],
  "count": 4
}
```

### 2. Çalışan Detayları
**GET** `/api/v1/employees/:id`

Belirli bir çalışanın detaylarını getirir.

**Headers:**
```
Authorization: Bearer <token>
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "string",
    "branchId": "string|null",
    "profileImage": "string|null"
  }
}
```

## 🔧 Test Hesapları

Seed script ile oluşturulan test hesapları:

| Rol | Kullanıcı Adı | PIN | İsim |
|-----|---------------|-----|------|
| Manager | manager1 | 1234 | Ahmet Yılmaz |
| Cashier | cashier1 | 5678 | Ayşe Demir |
| Waiter | waiter1 | 9999 | Mehmet Kaya |
| Kitchen | kitchen1 | 1111 | Fatma Özkan |

## 🧪 Test Komutları (PowerShell)

### Aktif Çalışanları Getir:
```powershell
Invoke-RestMethod -Uri "http://localhost:3001/api/v1/employees/active-shift" -Method GET
```

### PIN ile Giriş:
```powershell
$body = @{ employeeId = "EMPLOYEE_ID"; pin = "1234" } | ConvertTo-Json
Invoke-RestMethod -Uri "http://localhost:3001/api/v1/auth/pin-login" -Method POST -Body $body -ContentType "application/json"
```

### Token ile Profil:
```powershell
$headers = @{ Authorization = "Bearer YOUR_TOKEN" }
Invoke-RestMethod -Uri "http://localhost:3001/api/v1/auth/profile" -Method GET -Headers $headers
```

## 🛡️ Güvenlik Özellikleri

- ✅ PIN'ler bcrypt ile hash'leniyor
- ✅ JWT token'lar güvenli secret ile imzalanıyor
- ✅ Rate limiting (5 deneme/10 dakika)
- ✅ Session yönetimi
- ✅ Rol bazlı yetkilendirme
- ✅ Input validation (Zod)
- ✅ Error handling

## 🚀 Kurulum ve Çalıştırma

```bash
# Bağımlılıkları yükle
npm install

# Veritabanını hazırla
npm run db:generate
npm run db:migrate

# Test verilerini oluştur
npm run db:seed

# Geliştirme modunda çalıştır
npm run dev

# Production build
npm run build
npm start
```
