
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  taxNumber: 'taxNumber',
  taxOffice: 'taxOffice',
  address: 'address',
  phone: 'phone',
  email: 'email',
  logo: 'logo',
  eArchiveUsername: 'eArchiveUsername',
  eArchivePassword: 'eArchivePassword',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BranchScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  code: 'code',
  name: 'name',
  address: 'address',
  phone: 'phone',
  email: 'email',
  serverIp: 'serverIp',
  serverPort: 'serverPort',
  isMainBranch: 'isMainBranch',
  openingTime: 'openingTime',
  closingTime: 'closingTime',
  cashRegisterId: 'cashRegisterId',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  branchId: 'branchId',
  username: 'username',
  password: 'password',
  pin: 'pin',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  role: 'role',
  permissions: 'permissions',
  active: 'active',
  lastLoginAt: 'lastLoginAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  branchId: 'branchId',
  token: 'token',
  deviceInfo: 'deviceInfo',
  startedAt: 'startedAt',
  endedAt: 'endedAt',
  lastActivityAt: 'lastActivityAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  parentId: 'parentId',
  name: 'name',
  description: 'description',
  image: 'image',
  color: 'color',
  icon: 'icon',
  displayOrder: 'displayOrder',
  active: 'active',
  printerGroupId: 'printerGroupId',
  preparationTime: 'preparationTime',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  categoryId: 'categoryId',
  code: 'code',
  barcode: 'barcode',
  name: 'name',
  description: 'description',
  image: 'image',
  basePrice: 'basePrice',
  taxId: 'taxId',
  trackStock: 'trackStock',
  unit: 'unit',
  criticalStock: 'criticalStock',
  available: 'available',
  sellable: 'sellable',
  preparationTime: 'preparationTime',
  hasVariants: 'hasVariants',
  hasModifiers: 'hasModifiers',
  displayOrder: 'displayOrder',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  syncId: 'syncId',
  lastSyncAt: 'lastSyncAt'
};

exports.Prisma.ProductVariantScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  name: 'name',
  code: 'code',
  price: 'price',
  displayOrder: 'displayOrder',
  active: 'active'
};

exports.Prisma.ModifierGroupScalarFieldEnum = {
  id: 'id',
  name: 'name',
  minSelection: 'minSelection',
  maxSelection: 'maxSelection',
  required: 'required',
  displayOrder: 'displayOrder',
  active: 'active'
};

exports.Prisma.ModifierScalarFieldEnum = {
  id: 'id',
  groupId: 'groupId',
  name: 'name',
  price: 'price',
  displayOrder: 'displayOrder',
  active: 'active'
};

exports.Prisma.ProductModifierGroupScalarFieldEnum = {
  productId: 'productId',
  modifierGroupId: 'modifierGroupId',
  displayOrder: 'displayOrder'
};

exports.Prisma.InventoryItemScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  name: 'name',
  code: 'code',
  unit: 'unit',
  currentStock: 'currentStock',
  criticalLevel: 'criticalLevel',
  lastCost: 'lastCost',
  averageCost: 'averageCost',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RecipeScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  name: 'name',
  yield: 'yield',
  active: 'active',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RecipeItemScalarFieldEnum = {
  id: 'id',
  recipeId: 'recipeId',
  inventoryItemId: 'inventoryItemId',
  quantity: 'quantity',
  unit: 'unit'
};

exports.Prisma.TableAreaScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  name: 'name',
  displayOrder: 'displayOrder',
  active: 'active'
};

exports.Prisma.TableScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  areaId: 'areaId',
  number: 'number',
  capacity: 'capacity',
  positionX: 'positionX',
  positionY: 'positionY',
  width: 'width',
  height: 'height',
  shape: 'shape',
  status: 'status',
  active: 'active'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  orderNumber: 'orderNumber',
  orderType: 'orderType',
  tableId: 'tableId',
  customerCount: 'customerCount',
  customerId: 'customerId',
  customerName: 'customerName',
  customerPhone: 'customerPhone',
  deliveryAddress: 'deliveryAddress',
  status: 'status',
  paymentStatus: 'paymentStatus',
  subtotal: 'subtotal',
  discountAmount: 'discountAmount',
  discountRate: 'discountRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  paidAmount: 'paidAmount',
  tipAmount: 'tipAmount',
  waiterId: 'waiterId',
  cashierId: 'cashierId',
  orderNote: 'orderNote',
  kitchenNote: 'kitchenNote',
  orderedAt: 'orderedAt',
  completedAt: 'completedAt',
  cancelledAt: 'cancelledAt',
  syncId: 'syncId',
  lastSyncAt: 'lastSyncAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  variantId: 'variantId',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  discountAmount: 'discountAmount',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  status: 'status',
  sentToKitchenAt: 'sentToKitchenAt',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  servedAt: 'servedAt',
  cancelledAt: 'cancelledAt',
  note: 'note',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemModifierScalarFieldEnum = {
  id: 'id',
  orderItemId: 'orderItemId',
  modifierId: 'modifierId',
  quantity: 'quantity',
  price: 'price'
};

exports.Prisma.PaymentMethodScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  code: 'code',
  type: 'type',
  commissionRate: 'commissionRate',
  requiresApproval: 'requiresApproval',
  displayOrder: 'displayOrder',
  active: 'active'
};

exports.Prisma.PaymentScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  paymentMethodId: 'paymentMethodId',
  amount: 'amount',
  tipAmount: 'tipAmount',
  approvalCode: 'approvalCode',
  maskedCardNumber: 'maskedCardNumber',
  status: 'status',
  paidAt: 'paidAt',
  refundedAt: 'refundedAt',
  cashMovementId: 'cashMovementId'
};

exports.Prisma.TaxScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  name: 'name',
  rate: 'rate',
  code: 'code',
  isDefault: 'isDefault',
  active: 'active'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  invoiceType: 'invoiceType',
  serialNo: 'serialNo',
  sequenceNo: 'sequenceNo',
  customerName: 'customerName',
  customerTaxNo: 'customerTaxNo',
  customerTaxOffice: 'customerTaxOffice',
  customerAddress: 'customerAddress',
  customerPhone: 'customerPhone',
  customerEmail: 'customerEmail',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  totalAmount: 'totalAmount',
  uuid: 'uuid',
  eArchiveStatus: 'eArchiveStatus',
  eArchiveResponse: 'eArchiveResponse',
  createdAt: 'createdAt',
  printedAt: 'printedAt',
  cancelledAt: 'cancelledAt'
};

exports.Prisma.CashMovementScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  userId: 'userId',
  type: 'type',
  amount: 'amount',
  description: 'description',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  previousBalance: 'previousBalance',
  currentBalance: 'currentBalance',
  createdAt: 'createdAt'
};

exports.Prisma.DailyReportScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  reportDate: 'reportDate',
  totalOrders: 'totalOrders',
  totalItems: 'totalItems',
  grossSales: 'grossSales',
  totalDiscount: 'totalDiscount',
  netSales: 'netSales',
  totalTax: 'totalTax',
  totalSales: 'totalSales',
  cashSales: 'cashSales',
  creditCardSales: 'creditCardSales',
  otherSales: 'otherSales',
  openingBalance: 'openingBalance',
  closingBalance: 'closingBalance',
  zReportNo: 'zReportNo',
  zReportTime: 'zReportTime',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.StockMovementScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  productId: 'productId',
  inventoryItemId: 'inventoryItemId',
  type: 'type',
  quantity: 'quantity',
  unit: 'unit',
  unitCost: 'unitCost',
  totalCost: 'totalCost',
  referenceId: 'referenceId',
  referenceType: 'referenceType',
  note: 'note',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  companyName: 'companyName',
  taxNumber: 'taxNumber',
  taxOffice: 'taxOffice',
  phone: 'phone',
  email: 'email',
  address: 'address',
  loyaltyPoints: 'loyaltyPoints',
  totalSpent: 'totalSpent',
  orderCount: 'orderCount',
  currentDebt: 'currentDebt',
  creditLimit: 'creditLimit',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PrinterScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  name: 'name',
  ipAddress: 'ipAddress',
  port: 'port',
  type: 'type',
  paperWidth: 'paperWidth',
  active: 'active'
};

exports.Prisma.PrinterGroupScalarFieldEnum = {
  id: 'id',
  printerId: 'printerId',
  name: 'name'
};

exports.Prisma.PriceOverrideScalarFieldEnum = {
  id: 'id',
  productId: 'productId',
  branchId: 'branchId',
  price: 'price',
  startDate: 'startDate',
  endDate: 'endDate',
  startTime: 'startTime',
  endTime: 'endTime',
  daysOfWeek: 'daysOfWeek',
  active: 'active'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  branchId: 'branchId',
  action: 'action',
  entityType: 'entityType',
  entityId: 'entityId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  createdAt: 'createdAt'
};

exports.Prisma.OrderLogScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  action: 'action',
  details: 'details',
  createdAt: 'createdAt',
  createdBy: 'createdBy'
};

exports.Prisma.SyncLogScalarFieldEnum = {
  id: 'id',
  branchId: 'branchId',
  syncType: 'syncType',
  direction: 'direction',
  recordCount: 'recordCount',
  successCount: 'successCount',
  failureCount: 'failureCount',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  error: 'error',
  details: 'details'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  BRANCH_MANAGER: 'BRANCH_MANAGER',
  CASHIER: 'CASHIER',
  WAITER: 'WAITER',
  KITCHEN: 'KITCHEN',
  REPORTER: 'REPORTER'
};

exports.ProductUnit = exports.$Enums.ProductUnit = {
  PIECE: 'PIECE',
  KG: 'KG',
  GRAM: 'GRAM',
  LITER: 'LITER',
  ML: 'ML',
  PORTION: 'PORTION'
};

exports.TableShape = exports.$Enums.TableShape = {
  RECTANGLE: 'RECTANGLE',
  CIRCLE: 'CIRCLE',
  SQUARE: 'SQUARE'
};

exports.TableStatus = exports.$Enums.TableStatus = {
  EMPTY: 'EMPTY',
  OCCUPIED: 'OCCUPIED',
  RESERVED: 'RESERVED',
  CLEANING: 'CLEANING',
  UNAVAILABLE: 'UNAVAILABLE'
};

exports.OrderType = exports.$Enums.OrderType = {
  DINE_IN: 'DINE_IN',
  TAKEAWAY: 'TAKEAWAY',
  DELIVERY: 'DELIVERY',
  ONLINE: 'ONLINE'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  PREPARING: 'PREPARING',
  READY: 'READY',
  DELIVERING: 'DELIVERING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.PaymentStatus = exports.$Enums.PaymentStatus = {
  UNPAID: 'UNPAID',
  PARTIAL: 'PARTIAL',
  PAID: 'PAID',
  REFUNDED: 'REFUNDED'
};

exports.OrderItemStatus = exports.$Enums.OrderItemStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  PREPARING: 'PREPARING',
  READY: 'READY',
  SERVED: 'SERVED',
  CANCELLED: 'CANCELLED',
  VOID: 'VOID'
};

exports.PaymentMethodType = exports.$Enums.PaymentMethodType = {
  CASH: 'CASH',
  CREDIT_CARD: 'CREDIT_CARD',
  DEBIT_CARD: 'DEBIT_CARD',
  MEAL_CARD: 'MEAL_CARD',
  MOBILE: 'MOBILE',
  TRANSFER: 'TRANSFER',
  OTHER: 'OTHER'
};

exports.InvoiceType = exports.$Enums.InvoiceType = {
  RECEIPT: 'RECEIPT',
  INVOICE: 'INVOICE',
  E_ARCHIVE: 'E_ARCHIVE',
  E_INVOICE: 'E_INVOICE'
};

exports.EArchiveStatus = exports.$Enums.EArchiveStatus = {
  PENDING: 'PENDING',
  SENT: 'SENT',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  CANCELLED: 'CANCELLED'
};

exports.CashMovementType = exports.$Enums.CashMovementType = {
  SALE: 'SALE',
  REFUND: 'REFUND',
  EXPENSE: 'EXPENSE',
  INCOME: 'INCOME',
  OPENING: 'OPENING',
  CLOSING: 'CLOSING',
  DEPOSIT: 'DEPOSIT',
  WITHDRAWAL: 'WITHDRAWAL'
};

exports.StockMovementType = exports.$Enums.StockMovementType = {
  PURCHASE: 'PURCHASE',
  SALE: 'SALE',
  RETURN: 'RETURN',
  WASTE: 'WASTE',
  TRANSFER_IN: 'TRANSFER_IN',
  TRANSFER_OUT: 'TRANSFER_OUT',
  ADJUSTMENT: 'ADJUSTMENT',
  PRODUCTION: 'PRODUCTION',
  CONSUMPTION: 'CONSUMPTION'
};

exports.PrinterType = exports.$Enums.PrinterType = {
  RECEIPT: 'RECEIPT',
  KITCHEN: 'KITCHEN',
  LABEL: 'LABEL'
};

exports.Prisma.ModelName = {
  Company: 'Company',
  Branch: 'Branch',
  User: 'User',
  Session: 'Session',
  Category: 'Category',
  Product: 'Product',
  ProductVariant: 'ProductVariant',
  ModifierGroup: 'ModifierGroup',
  Modifier: 'Modifier',
  ProductModifierGroup: 'ProductModifierGroup',
  InventoryItem: 'InventoryItem',
  Recipe: 'Recipe',
  RecipeItem: 'RecipeItem',
  TableArea: 'TableArea',
  Table: 'Table',
  Order: 'Order',
  OrderItem: 'OrderItem',
  OrderItemModifier: 'OrderItemModifier',
  PaymentMethod: 'PaymentMethod',
  Payment: 'Payment',
  Tax: 'Tax',
  Invoice: 'Invoice',
  CashMovement: 'CashMovement',
  DailyReport: 'DailyReport',
  StockMovement: 'StockMovement',
  Customer: 'Customer',
  Printer: 'Printer',
  PrinterGroup: 'PrinterGroup',
  PriceOverride: 'PriceOverride',
  AuditLog: 'AuditLog',
  OrderLog: 'OrderLog',
  SyncLog: 'SyncLog'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
