// Dashboard API için TypeScript tipleri
import { OrderStatus, PaymentStatus, OrderItemStatus } from '../generated/client';

// ==================== DASHBOARD METRICS ====================

export interface DashboardMetrics {
  // Günlük toplam kazanç
  dailyRevenue: {
    amount: number;
    changeFromYesterday: number; // Yüzde değişim
  };
  
  // İşlemdeki sipariş sayısı
  ordersInProgress: {
    count: number;
    changeFromYesterday: number;
  };
  
  // Ödeme bekleyen sipariş sayısı
  ordersWaitingPayment: {
    count: number;
    changeFromYesterday: number;
  };
  
  // Ek metrikler
  totalOrdersToday: number;
  averageOrderValue: number;
}

// ==================== POPULAR PRODUCTS ====================

export interface PopularProduct {
  id: string;
  name: string;
  categoryName: string;
  totalQuantity: number;
  totalOrders: number;
  revenue: number;
  image?: string;
}

// ==================== OUT OF STOCK PRODUCTS ====================

export interface OutOfStockProduct {
  id: string;
  name: string;
  categoryName: string;
  estimatedRestockTime?: string; // "Yarın", "16:20'de hazır" gibi
  image?: string;
}

// ==================== ORDER SUMMARIES ====================

export interface OrderSummary {
  id: string;
  orderNumber: string;
  customerName?: string;
  tableNumber?: string;
  itemCount: number;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  orderedAt: Date;
  estimatedReadyTime?: Date;
  
  // Sipariş durumu açıklaması
  statusDescription: string; // "Mutfakta", "Pişiriliyor", "Servise Hazır"
  
  // Sipariş öğeleri özeti
  items: OrderItemSummary[];
}

export interface OrderItemSummary {
  id: string;
  productName: string;
  quantity: number;
  status: OrderItemStatus;
  statusDescription: string;
  note?: string;
}

// ==================== MAIN DASHBOARD DATA ====================

export interface DashboardData {
  metrics: DashboardMetrics;
  popularProducts: PopularProduct[];
  outOfStockProducts: OutOfStockProduct[];
  ordersInProgress: OrderSummary[];
  ordersWaitingPayment: OrderSummary[];
  
  // Meta bilgiler
  lastUpdated: Date;
  branchId: string;
  branchName: string;
}

// ==================== SERVICE INTERFACES ====================

export interface DashboardServiceInterface {
  getDashboardData(branchId: string): Promise<DashboardData>;
  getMetrics(branchId: string): Promise<DashboardMetrics>;
  getPopularProducts(branchId: string, limit?: number): Promise<PopularProduct[]>;
  getOutOfStockProducts(branchId: string): Promise<OutOfStockProduct[]>;
  getOrdersInProgress(branchId: string): Promise<OrderSummary[]>;
  getOrdersWaitingPayment(branchId: string): Promise<OrderSummary[]>;
}

// ==================== HELPER TYPES ====================

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface MetricComparison {
  current: number;
  previous: number;
  changePercent: number;
}

// Status açıklamaları için mapping
export const ORDER_STATUS_DESCRIPTIONS: Record<OrderStatus, string> = {
  PENDING: 'Bekliyor',
  CONFIRMED: 'Onaylandı',
  PREPARING: 'Hazırlanıyor',
  READY: 'Hazır',
  DELIVERING: 'Teslimatta',
  COMPLETED: 'Tamamlandı',
  CANCELLED: 'İptal Edildi',
};

export const ORDER_ITEM_STATUS_DESCRIPTIONS: Record<OrderItemStatus, string> = {
  PENDING: 'Bekliyor',
  SENT: 'Mutfağa Gönderildi',
  PREPARING: 'Pişiriliyor',
  READY: 'Servise Hazır',
  SERVED: 'Servis Edildi',
  CANCELLED: 'İptal Edildi',
  VOID: 'Geçersiz',
};

export const PAYMENT_STATUS_DESCRIPTIONS: Record<PaymentStatus, string> = {
  UNPAID: 'Ödenmedi',
  PARTIAL: 'Kısmi Ödendi',
  PAID: 'Ödendi',
  REFUNDED: 'İade Edildi',
};
