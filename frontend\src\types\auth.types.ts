// Auth ile ilgili TypeScript tipleri
export interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  role: 'BRANCH_MANAGER' | 'CASHIER' | 'WAITER' | 'KITCHEN';
  branchId: string | null;
  profileImage?: string | null;
}

export interface LoginRequest {
  employeeId: string;
  pin: string;
}

export interface LoginResponse {
  token: string;
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
    branchId: string | null;
    email: string | null;
    phone: string | null;
  };
  session: {
    id: string;
    startedAt: string;
    branchId: string;
  };
}

export interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  employee: LoginResponse['employee'] | null;
  session: LoginResponse['session'] | null;
}

export interface MotivationalQuote {
  text: string;
  author: string;
}
