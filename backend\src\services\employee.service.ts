// Employee service - Business logic
import { PrismaClient, UserRole } from '../generated/client';
import { ActiveEmployee } from '../types/auth.types';

export class EmployeeService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Aktif vardi<PERSON> olan çalışanları getirir
   */
  async getActiveShiftEmployees(branchId?: string): Promise<ActiveEmployee[]> {
    const whereCondition: any = {
      active: true,
      pin: {
        not: null, // PIN'i olan <PERSON>
      },
      role: {
        in: [
          UserRole.CASHIER,
          UserRole.WAITER,
          UserRole.KITCHEN,
          UserRole.BRANCH_MANAGER,
        ],
      },
    };

    // Şube filtresi
    if (branchId) {
      whereCondition.OR = [
        { branchId: branchId },
        { branchId: null }, // Tüm şubelere erişimi olan <PERSON>
      ];
    }

    const employees = await this.prisma.user.findMany({
      where: whereCondition,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        role: true,
        branchId: true,
        email: true,
        phone: true,
      },
      orderBy: [
        { role: 'asc' },
        { firstName: 'asc' },
      ],
    });

    return employees.map(emp => ({
      id: emp.id,
      firstName: emp.firstName,
      lastName: emp.lastName,
      role: emp.role,
      branchId: emp.branchId,
      profileImage: null, // Şu an schema'da yok, gelecekte eklenebilir
    }));
  }

  /**
   * Çalışan detaylarını getirir (güvenli)
   */
  async getEmployeeById(employeeId: string): Promise<ActiveEmployee | null> {
    const employee = await this.prisma.user.findUnique({
      where: { 
        id: employeeId,
        active: true,
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        role: true,
        branchId: true,
        email: true,
        phone: true,
      },
    });

    if (!employee) {
      return null;
    }

    return {
      id: employee.id,
      firstName: employee.firstName,
      lastName: employee.lastName,
      role: employee.role,
      branchId: employee.branchId,
      profileImage: null,
    };
  }

  /**
   * Çalışanın PIN'inin olup olmadığını kontrol eder
   */
  async hasPin(employeeId: string): Promise<boolean> {
    const employee = await this.prisma.user.findUnique({
      where: { id: employeeId },
      select: { pin: true },
    });

    return !!(employee?.pin);
  }

  /**
   * Çalışan PIN'ini günceller (hash'leyerek)
   */
  async updatePin(employeeId: string, newPin: string): Promise<void> {
    const { HashUtils } = await import('../utils/hash.utils');
    const hashedPin = await HashUtils.hashPin(newPin);

    await this.prisma.user.update({
      where: { id: employeeId },
      data: { pin: hashedPin },
    });
  }

  /**
   * Şube bazlı çalışan sayısını getirir
   */
  async getEmployeeCountByBranch(branchId: string): Promise<number> {
    return this.prisma.user.count({
      where: {
        branchId: branchId,
        active: true,
      },
    });
  }

  /**
   * Aktif session'ları olan çalışanları getirir
   */
  async getActiveSessionEmployees(branchId?: string): Promise<ActiveEmployee[]> {
    const whereCondition: any = {
      user: {
        active: true,
      },
      endedAt: null,
    };

    if (branchId) {
      whereCondition.branchId = branchId;
    }

    const sessions = await this.prisma.session.findMany({
      where: whereCondition,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            role: true,
            branchId: true,
            email: true,
            phone: true,
          },
        },
      },
      orderBy: {
        startedAt: 'desc',
      },
    });

    return sessions.map(session => ({
      id: session.user.id,
      firstName: session.user.firstName,
      lastName: session.user.lastName,
      role: session.user.role,
      branchId: session.user.branchId,
      profileImage: null,
    }));
  }
}
