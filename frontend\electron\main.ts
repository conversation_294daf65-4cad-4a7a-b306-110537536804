const { app, BrowserWindow } = require('electron');
const path = require('path');

// Development mode kontrolü - <PERSON><PERSON> g<PERSON> yöntem
const isDev = !app.isPackaged || process.env.NODE_ENV === 'development';

function createWindow() {
  const mainWindow = new BrowserWindow({
    height: 800,
    width: 1200,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false, // Development için gerekli
    },
    show: false, // Yükleme tamamlanana kadar gizle
  });

  // Pencereyi yükleme tamamlandığında göster
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  if (isDev) {
    // Vite dev server portu (5173)
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();

    // Reload on change
    mainWindow.webContents.on('did-fail-load', () => {
      setTimeout(() => {
        mainWindow.loadURL('http://localhost:5173');
      }, 1000);
    });
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }
}

app.whenReady().then(() => {
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
