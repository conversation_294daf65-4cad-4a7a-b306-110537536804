// Employee selector dropdown component
import React, { useState } from 'react';
import { Employee } from '../../types/auth.types';
import { LoadingSpinner } from '../common/LoadingSpinner';

interface EmployeeSelectorProps {
  employees: Employee[];
  selectedEmployee: Employee | null;
  onEmployeeSelect: (employee: Employee) => void;
  isLoading?: boolean;
}

export const EmployeeSelector: React.FC<EmployeeSelectorProps> = ({
  employees,
  selectedEmployee,
  onEmployeeSelect,
  isLoading = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      BRANCH_MANAGER: '<PERSON><PERSON> Müdürü',
      CASHIER: 'Ka<PERSON><PERSON>',
      WAITER: 'Garson',
      KITCHEN: 'Mutfak',
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  const getShiftTime = (employee: Employee) => {
    // Şu an için sabit de<PERSON>, gelecekte API'den gelecek
    const shiftTimes = {
      BRANCH_MANAGER: '09:00 PM - 11:00 PM',
      CASHIER: '10:00 PM - 12:00 PM',
      WAITER: '11:00 PM - 01:00 PM',
      KITCHEN: '09:00 PM - 11:00 PM',
    };
    return shiftTimes[employee.role as keyof typeof shiftTimes] || '09:00 PM - 11:00 PM';
  };

  if (isLoading) {
    return (
      <div className="w-full max-w-md">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Hesabınızı seçin
        </label>
        <div className="relative">
          <div className="w-full p-4 border border-gray-300 rounded-lg bg-gray-50">
            <LoadingSpinner size="sm" className="mx-auto" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Hesabınızı seçin
      </label>
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full p-4 text-left border border-gray-300 rounded-lg bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {selectedEmployee ? (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-gray-600">
                  {selectedEmployee.firstName.charAt(0)}{selectedEmployee.lastName.charAt(0)}
                </span>
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">
                  {selectedEmployee.firstName} {selectedEmployee.lastName}
                </div>
                <div className="text-sm text-gray-500">
                  {getRoleDisplayName(selectedEmployee.role)} • {getShiftTime(selectedEmployee)}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">Çalışan seçin</div>
          )}
          <div className="absolute inset-y-0 right-0 flex items-center pr-4">
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
            {employees.map((employee) => (
              <button
                key={employee.id}
                type="button"
                onClick={() => {
                  onEmployeeSelect(employee);
                  setIsOpen(false);
                }}
                className="w-full p-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-gray-600">
                      {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {employee.firstName} {employee.lastName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {getRoleDisplayName(employee.role)} • {getShiftTime(employee)}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
