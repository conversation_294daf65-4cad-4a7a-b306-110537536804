// Motivational quotes component
import React, { useState, useEffect } from 'react';
import { MotivationalQuote } from '../../types/auth.types';

const quotes: MotivationalQuote[] = [
  {
    text: "Every contact we have with a customer influences whether or not they'll come back. We have to be great every time or we'll lose them.",
    author: "<PERSON>, Former"
  },
  {
    text: "If you do build a great experience, customers tell each other about that. Word of mouth is very powerful.",
    author: "<PERSON>, Founder of Amazon"
  },
  {
    text: "Müşteri memnuniyeti bizim en büyük önceliğimizdir. Her gülümseme, her teşekkür bizim için değerlidir.",
    author: "Atorpos E<PERSON>"
  },
  {
    text: "Başarı, hazırlık ile fırsatın buluştuğu andır. Her vardiya yeni bir fırsat demektir.",
    author: "Atorpos Ekibi"
  },
  {
    text: "Takı<PERSON> çalışması, birey<PERSON> yeteneklerin ortak bir hedefe yönlendirilmesidir.",
    author: "<PERSON><PERSON><PERSON><PERSON>"
  },
  {
    text: "<PERSON><PERSON> bir <PERSON><PERSON><PERSON>, her zaman akıllı çabanın sonucudur.",
    author: "John Ruskin"
  }
];

export const MotivationalQuotes: React.FC = () => {
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false);
      
      setTimeout(() => {
        setCurrentQuoteIndex((prevIndex) => 
          prevIndex === quotes.length - 1 ? 0 : prevIndex + 1
        );
        setIsVisible(true);
      }, 500); // Fade out duration
    }, 8000); // Change quote every 8 seconds

    return () => clearInterval(interval);
  }, []);

  const currentQuote = quotes[currentQuoteIndex];

  return (
    <div className="max-w-lg">
      <div
        className={`
          transition-opacity duration-500 ease-in-out
          ${isVisible ? 'opacity-100' : 'opacity-0'}
        `}
      >
        <blockquote className="text-white text-lg md:text-xl leading-relaxed mb-4">
          "{currentQuote.text}"
        </blockquote>
        <cite className="text-white/80 text-sm font-medium">
          — {currentQuote.author}
        </cite>
      </div>
      
      {/* Quote indicators */}
      <div className="flex space-x-2 mt-6">
        {quotes.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              setIsVisible(false);
              setTimeout(() => {
                setCurrentQuoteIndex(index);
                setIsVisible(true);
              }, 250);
            }}
            className={`
              w-2 h-2 rounded-full transition-all duration-300
              ${index === currentQuoteIndex 
                ? 'bg-white' 
                : 'bg-white/40 hover:bg-white/60'
              }
            `}
          />
        ))}
      </div>
    </div>
  );
};
