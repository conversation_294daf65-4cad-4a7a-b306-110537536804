// Auth hook
import { useState } from 'react';
import { useAuthStore } from '../store/authStore';
import { apiService } from '../services/api.service';
import { LoginRequest } from '../types/auth.types';
import toast from 'react-hot-toast';

export const useAuth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { login, logout, isAuthenticated, employee } = useAuthStore();

  const handleLogin = async (loginData: LoginRequest) => {
    setIsLoading(true);
    try {
      const response = await apiService.pinLogin(loginData);
      login(response);
      toast.success(`<PERSON><PERSON> geldiniz, ${response.employee.firstName}!`);
      return response;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Giriş yapılamadı';
      toast.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await apiService.logout();
      logout();
      toast.success('Başarıyla çıkış yapıldı');
    } catch (error) {
      // Hata olsa bile logout yap
      logout();
      toast.success('Çıkış yapıldı');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyAuth = async () => {
    if (!isAuthenticated) return false;
    
    try {
      const isValid = await apiService.verifyToken();
      if (!isValid) {
        logout();
        return false;
      }
      return true;
    } catch (error) {
      logout();
      return false;
    }
  };

  return {
    isLoading,
    isAuthenticated,
    employee,
    login: handleLogin,
    logout: handleLogout,
    verifyAuth,
  };
};
