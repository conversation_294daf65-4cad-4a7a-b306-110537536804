// Hash utility fonksiyonları (bcrypt)
import bcrypt from 'bcrypt';

export class HashUtils {
  private static readonly SALT_ROUNDS = 12;

  /**
   * Metni hash'ler (PIN, password vb.)
   */
  static async hashText(text: string): Promise<string> {
    try {
      return await bcrypt.hash(text, this.SALT_ROUNDS);
    } catch (error) {
      throw new Error('Hash oluşturma hatası');
    }
  }

  /**
   * Hash'lenmiş metin ile plain text'i karşılaştırır
   */
  static async compareText(plainText: string, hashedText: string): Promise<boolean> {
    try {
      return await bcrypt.compare(plainText, hashedText);
    } catch (error) {
      throw new Error('Hash karşılaştırma hatası');
    }
  }

  /**
   * PIN için özel hash fonksiyonu
   */
  static async hashPin(pin: string): Promise<string> {
    // PIN'lerin 4-6 haneli olduğunu varsayıyoruz
    if (!/^\d{4,6}$/.test(pin)) {
      throw new Error('PIN 4-6 haneli sayı olmalıdır');
    }
    return this.hashText(pin);
  }

  /**
   * PIN doğrulama
   */
  static async verifyPin(pin: string, hashedPin: string): Promise<boolean> {
    if (!/^\d{4,6}$/.test(pin)) {
      return false;
    }
    return this.compareText(pin, hashedPin);
  }
}
