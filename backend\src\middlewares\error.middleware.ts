// Merkezi error handling middleware
import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';

export interface AppError extends Error {
  statusCode?: number;
  code?: string;
}

export class ErrorHandler {
  /**
   * Merkezi error handling middleware
   */
  static handle(err: AppError, req: Request, res: Response, next: NextFunction): void {
    console.error('Error:', {
      message: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString(),
    });

    // Zod validation hatası
    if (err instanceof ZodError) {
      res.status(400).json({
        error: 'Validation Error',
        message: 'Gönderilen veriler geçersiz',
        details: err.issues.map((issue: any) => ({
          field: issue.path.join('.'),
          message: issue.message,
        })),
      });
      return;
    }

    // <PERSON>zel uygulama hataları
    if (err.statusCode) {
      res.status(err.statusCode).json({
        error: err.code || 'Application Error',
        message: err.message,
      });
      return;
    }

    // JWT hataları
    if (err.message.includes('Token') || err.message.includes('token')) {
      res.status(401).json({
        error: 'Authentication Error',
        message: err.message,
      });
      return;
    }

    // Prisma hataları
    if (err.message.includes('Prisma') || err.message.includes('database')) {
      res.status(500).json({
        error: 'Database Error',
        message: 'Veritabanı hatası oluştu',
      });
      return;
    }

    // Genel server hatası
    res.status(500).json({
      error: 'Internal Server Error',
      message: process.env.NODE_ENV === 'development' ? err.message : 'Bir hata oluştu',
    });
  }

  /**
   * 404 handler
   */
  static notFound(req: Request, res: Response): void {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.method} ${req.path} bulunamadı`,
    });
  }

  /**
   * Özel hata oluşturucu
   */
  static createError(message: string, statusCode: number, code?: string): AppError {
    const error = new Error(message) as AppError;
    error.statusCode = statusCode;
    error.code = code;
    return error;
  }
}
