import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { PrismaClient } from './generated/client';

// Routes
import authRoutes from './routes/auth.routes';
import employeeRoutes from './routes/employee.routes';
import dashboardRoutes from './routes/dashboard.routes';

// Middlewares
import { ErrorHandler } from './middlewares/error.middleware';

dotenv.config();

const prisma = new PrismaClient();
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Atorpos POS Backend is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'Atorpos POS API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/v1/auth',
      employees: '/api/v1/employees',
      health: '/health'
    }
  });
});

// API Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/employees', employeeRoutes);
app.use('/api/v1/dashboard', dashboardRoutes);

// 404 handler
app.use('*', ErrorHandler.notFound);

// Error handling middleware (en sonda olmalı)
app.use(ErrorHandler.handle);

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🔄 Shutting down gracefully...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🔄 Shutting down gracefully...');
  await prisma.$disconnect();
  process.exit(0);
});

app.listen(PORT, async () => {
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    console.log('🔧 Environment:', process.env.NODE_ENV || 'development');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/`);
});
