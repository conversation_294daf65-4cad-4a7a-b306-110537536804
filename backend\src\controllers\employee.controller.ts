// Employee controller - HTTP request handling
import { Request, Response, NextFunction } from 'express';
import { EmployeeService } from '../services/employee.service';
import { PrismaClient } from '../generated/client';

export class EmployeeController {
  private employeeService: EmployeeService;

  constructor() {
    this.employeeService = new EmployeeService(new PrismaClient());
  }

  /**
   * Aktif vardiyası olan çalışanları getirir
   * GET /api/v1/employees/active-shift
   */
  getActiveShiftEmployees = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const branchId = req.query.branchId as string;
      
      const employees = await this.employeeService.getActiveShiftEmployees(branchId);

      res.status(200).json({
        success: true,
        message: 'Aktif çalışanlar getirildi',
        data: employees,
        count: employees.length,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> detaylarını getirir
   * GET /api/v1/employees/:id
   */
  getEmployeeById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      
      const employee = await this.employeeService.getEmployeeById(id);

      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Çalışan bulunamadı',
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: employee,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Aktif session'ları olan çalışanları getirir
   * GET /api/v1/employees/active-sessions
   */
  getActiveSessionEmployees = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const branchId = req.query.branchId as string;
      
      const employees = await this.employeeService.getActiveSessionEmployees(branchId);

      res.status(200).json({
        success: true,
        message: 'Aktif session\'ları olan çalışanlar getirildi',
        data: employees,
        count: employees.length,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Şube bazlı çalışan sayısını getirir
   * GET /api/v1/employees/count/:branchId
   */
  getEmployeeCountByBranch = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { branchId } = req.params;
      
      const count = await this.employeeService.getEmployeeCountByBranch(branchId);

      res.status(200).json({
        success: true,
        data: { count },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Çalışanın PIN durumunu kontrol eder
   * GET /api/v1/employees/:id/pin-status
   */
  checkPinStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { id } = req.params;
      
      const hasPin = await this.employeeService.hasPin(id);

      res.status(200).json({
        success: true,
        data: { hasPin },
      });
    } catch (error) {
      next(error);
    }
  };
}
