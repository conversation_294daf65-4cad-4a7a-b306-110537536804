// JWT authentication middleware
import { Request, Response, NextFunction } from 'express';
import { JwtUtils } from '../utils/jwt.utils';
import { ErrorHandler } from './error.middleware';
import { JwtPayload } from '../types/auth.types';

// Request interface'ini g<PERSON>letiyo<PERSON>
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

export class AuthMiddleware {
  /**
   * JWT token doğrulama middleware
   */
  static authenticate(req: Request, res: Response, next: NextFunction): void {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader) {
        throw ErrorHandler.createError('Authorization header gereklidir', 401, 'MISSING_TOKEN');
      }

      const token = authHeader.startsWith('Bearer ') 
        ? authHeader.substring(7) 
        : authHeader;

      if (!token) {
        throw ErrorHandler.createError('Token gereklidir', 401, 'MISSING_TOKEN');
      }

      const payload = JwtUtils.verifyToken(token);
      req.user = payload;
      
      next();
    } catch (error) {
      next(error);
    }
  }

  /**
   * Rol bazlı yetkilendirme
   */
  static authorize(allowedRoles: string[]) {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        if (!req.user) {
          throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
        }

        if (!allowedRoles.includes(req.user.role)) {
          throw ErrorHandler.createError('Bu işlem için yetkiniz yok', 403, 'FORBIDDEN');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  /**
   * Şube bazlı yetkilendirme
   */
  static requireBranch(req: Request, res: Response, next: NextFunction): void {
    try {
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      if (!req.user.branchId) {
        throw ErrorHandler.createError('Şube bilgisi gereklidir', 403, 'BRANCH_REQUIRED');
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  /**
   * Opsiyonel authentication (token varsa doğrula, yoksa devam et)
   */
  static optionalAuth(req: Request, res: Response, next: NextFunction): void {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader) {
        const token = authHeader.startsWith('Bearer ') 
          ? authHeader.substring(7) 
          : authHeader;

        if (token && JwtUtils.isTokenValid(token)) {
          req.user = JwtUtils.verifyToken(token);
        }
      }

      next();
    } catch (error) {
      // Opsiyonel auth'da hata olursa devam et
      next();
    }
  }
}
