// Zod validation middleware
import { Request, Response, NextFunction } from 'express';
import { z, ZodSchema } from 'zod';

export class ValidationMiddleware {
  /**
   * Request body validation
   */
  static validateBody<T>(schema: ZodSchema<T>) {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        req.body = schema.parse(req.body);
        next();
      } catch (error) {
        next(error);
      }
    };
  }

  /**
   * Request params validation
   */
  static validateParams<T>(schema: ZodSchema<T>) {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        const validatedParams = schema.parse(req.params);
        req.params = validatedParams as any;
        next();
      } catch (error) {
        next(error);
      }
    };
  }

  /**
   * Request query validation
   */
  static validateQuery<T>(schema: ZodSchema<T>) {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        const validatedQuery = schema.parse(req.query);
        req.query = validatedQuery as any;
        next();
      } catch (error) {
        next(error);
      }
    };
  }
}

// Validation şemaları
export const PinLoginSchema = z.object({
  employeeId: z.string().min(1, 'Çalışan ID gereklidir'),
  pin: z.string().regex(/^\d{4,6}$/, 'PIN 4-6 haneli sayı olmalıdır'),
});

export const EmployeeParamsSchema = z.object({
  branchId: z.string().optional(),
});

export type PinLoginRequest = z.infer<typeof PinLoginSchema>;
export type EmployeeParamsRequest = z.infer<typeof EmployeeParamsSchema>;
