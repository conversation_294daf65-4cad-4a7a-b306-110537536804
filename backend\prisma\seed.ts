// Seed script - Test verileri oluşturur
import { PrismaClient, UserRole, OrderStatus, PaymentStatus, OrderItemStatus } from '../src/generated/client';
import { HashUtils } from '../src/utils/hash.utils';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Test şirketi oluştur
  const company = await prisma.company.upsert({
    where: { taxNumber: '1234567890' },
    update: {},
    create: {
      name: 'Test Restaurant',
      taxNumber: '1234567890',
      taxOffice: 'Test Vergi Dairesi',
      address: 'Test Adres',
      phone: '0212 123 45 67',
      email: '<EMAIL>',
    },
  });

  console.log('✅ Company created:', company.name);

  // Test şubesi oluştur
  const branch = await prisma.branch.upsert({
    where: { 
      companyId_code: {
        companyId: company.id,
        code: 'MAIN'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      code: '<PERSON><PERSON>',
      name: '<PERSON>',
      address: '<PERSON>',
      phone: '0212 123 45 67',
      email: '<EMAIL>',
      isMainBranch: true,
      openingTime: '09:00',
      closingTime: '23:00',
    },
  });

  console.log('✅ Branch created:', branch.name);

  // Test çalışanları oluştur
  const employees = [
    {
      username: 'manager1',
      firstName: 'Ahmet',
      lastName: 'Yılmaz',
      role: UserRole.BRANCH_MANAGER,
      pin: '123456',
    },
    {
      username: 'cashier1',
      firstName: 'Ayşe',
      lastName: 'Demir',
      role: UserRole.CASHIER,
      pin: '567856',
    },
    {
      username: 'waiter1',
      firstName: 'Mehmet',
      lastName: 'Kaya',
      role: UserRole.WAITER,
      pin: '999956',
    },
    {
      username: 'kitchen1',
      firstName: 'Fatma',
      lastName: 'Özkan',
      role: UserRole.KITCHEN,
      pin: '11116',
    },
  ];

  for (const emp of employees) {
    const hashedPin = await HashUtils.hashPin(emp.pin);
    
    const user = await prisma.user.upsert({
      where: { username: emp.username },
      update: {},
      create: {
        companyId: company.id,
        branchId: branch.id,
        username: emp.username,
        password: await HashUtils.hashText('password123'), // Default password
        pin: hashedPin,
        firstName: emp.firstName,
        lastName: emp.lastName,
        role: emp.role,
        active: true,
      },
    });

    console.log(`✅ Employee created: ${user.firstName} ${user.lastName} (${user.role}) - PIN: ${emp.pin}`);
  }

  // Test vergi oranları oluştur
  const taxes = [
    { name: 'KDV %8', rate: 8, code: 'VAT8' },
    { name: 'KDV %18', rate: 18, code: 'VAT18', isDefault: true },
  ];

  for (const tax of taxes) {
    await prisma.tax.upsert({
      where: {
        companyId_code: {
          companyId: company.id,
          code: tax.code,
        },
      },
      update: {},
      create: {
        companyId: company.id,
        name: tax.name,
        rate: tax.rate,
        code: tax.code,
        isDefault: tax.isDefault || false,
      },
    });
  }

  console.log('✅ Tax rates created');

  // Test ödeme yöntemleri oluştur
  const paymentMethods = [
    { name: 'Nakit', code: 'CASH', type: 'CASH' },
    { name: 'Kredi Kartı', code: 'CC', type: 'CREDIT_CARD' },
    { name: 'Yemek Kartı', code: 'MEAL', type: 'MEAL_CARD' },
  ];

  for (const pm of paymentMethods) {
    await prisma.paymentMethod.upsert({
      where: {
        companyId_code: {
          companyId: company.id,
          code: pm.code,
        },
      },
      update: {},
      create: {
        companyId: company.id,
        name: pm.name,
        code: pm.code,
        type: pm.type as any,
      },
    });
  }

  console.log('✅ Payment methods created');

  // Test kategorileri oluştur
  const categories = [
    { name: 'Ana Yemekler', displayOrder: 1 },
    { name: 'İçecekler', displayOrder: 2 },
    { name: 'Tatlılar', displayOrder: 3 },
    { name: 'Başlangıçlar', displayOrder: 4 },
  ];

  const createdCategories: any[] = [];
  for (const cat of categories) {
    const category = await prisma.category.upsert({
      where: {
        id: `${company.id}-${cat.name.replace(/\s+/g, '-').toLowerCase()}`,
      },
      update: {},
      create: {
        id: `${company.id}-${cat.name.replace(/\s+/g, '-').toLowerCase()}`,
        companyId: company.id,
        name: cat.name,
        displayOrder: cat.displayOrder,
      },
    });
    createdCategories.push(category);
  }

  console.log('✅ Categories created');

  // Varsayılan vergi oranını al
  const defaultTax = await prisma.tax.findFirst({
    where: { companyId: company.id, isDefault: true },
  });

  // Test ürünleri oluştur
  const products = [
    // Ana Yemekler
    { name: 'Adana Kebap', price: 85.00, categoryName: 'Ana Yemekler', available: true },
    { name: 'Tavuk Şiş', price: 75.00, categoryName: 'Ana Yemekler', available: true },
    { name: 'Karışık Izgara', price: 120.00, categoryName: 'Ana Yemekler', available: false }, // Stok dışı
    { name: 'Köfte', price: 65.00, categoryName: 'Ana Yemekler', available: true },

    // İçecekler
    { name: 'Çay', price: 8.00, categoryName: 'İçecekler', available: true },
    { name: 'Türk Kahvesi', price: 15.00, categoryName: 'İçecekler', available: true },
    { name: 'Ayran', price: 12.00, categoryName: 'İçecekler', available: true },
    { name: 'Kola', price: 18.00, categoryName: 'İçecekler', available: false }, // Stok dışı

    // Tatlılar
    { name: 'Baklava', price: 35.00, categoryName: 'Tatlılar', available: true },
    { name: 'Künefe', price: 45.00, categoryName: 'Tatlılar', available: true },

    // Başlangıçlar
    { name: 'Humus', price: 25.00, categoryName: 'Başlangıçlar', available: true },
    { name: 'Ezme', price: 20.00, categoryName: 'Başlangıçlar', available: true },
  ];

  const createdProducts: any[] = [];
  for (const prod of products) {
    const category = createdCategories.find(c => c.name === prod.categoryName);
    if (!category || !defaultTax) continue;

    const product = await prisma.product.upsert({
      where: {
        companyId_code: {
          companyId: company.id,
          code: prod.name.replace(/\s+/g, '_').toUpperCase(),
        },
      },
      update: {},
      create: {
        companyId: company.id,
        categoryId: category.id,
        code: prod.name.replace(/\s+/g, '_').toUpperCase(),
        name: prod.name,
        basePrice: prod.price,
        taxId: defaultTax.id,
        available: prod.available,
        sellable: true,
        preparationTime: Math.floor(Math.random() * 20) + 10, // 10-30 dakika
      },
    });
    createdProducts.push(product);
  }

  console.log('✅ Products created');

  // Test siparişleri oluştur
  const cashier = await prisma.user.findFirst({
    where: { username: 'cashier1' },
  });

  const waiter = await prisma.user.findFirst({
    where: { username: 'waiter1' },
  });

  const cashPaymentMethod = await prisma.paymentMethod.findFirst({
    where: { companyId: company.id, code: 'CASH' },
  });

  if (cashier && waiter && cashPaymentMethod && createdProducts.length > 0) {
    // Bugünün siparişleri
    const today = new Date();
    const orderStatuses = [OrderStatus.CONFIRMED, OrderStatus.PREPARING, OrderStatus.READY, OrderStatus.COMPLETED];
    const paymentStatuses = [PaymentStatus.UNPAID, PaymentStatus.PAID];

    for (let i = 1; i <= 8; i++) {
      const orderTime = new Date(today);
      orderTime.setHours(9 + i, Math.floor(Math.random() * 60));

      const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
      const paymentStatus = status === OrderStatus.COMPLETED
        ? paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)]
        : PaymentStatus.UNPAID;

      // Rastgele ürünler seç
      const selectedProducts = createdProducts
        .filter(p => p.available)
        .sort(() => 0.5 - Math.random())
        .slice(0, Math.floor(Math.random() * 3) + 1);

      let subtotal = 0;
      const orderItems = selectedProducts.map(product => {
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = Number(product.basePrice);
        const taxRate = Number(defaultTax!.rate);
        const taxAmount = (unitPrice * quantity * taxRate) / 100;
        const totalAmount = (unitPrice * quantity) + taxAmount;

        subtotal += totalAmount;

        return {
          product: {
            connect: { id: product.id }
          },
          quantity,
          unitPrice,
          taxRate,
          taxAmount,
          totalAmount,
          status: status === OrderStatus.COMPLETED ? OrderItemStatus.SERVED : OrderItemStatus.PREPARING,
        };
      });

      const taxAmount = subtotal * 0.15; // Yaklaşık vergi
      const totalAmount = subtotal + taxAmount;

      const order = await prisma.order.create({
        data: {
          branchId: branch.id,
          orderNumber: `2024-${String(i).padStart(4, '0')}`,
          orderType: 'DINE_IN',
          customerName: `Müşteri ${i}`,
          status: status as any,
          paymentStatus: paymentStatus as any,
          subtotal,
          taxAmount,
          totalAmount,
          waiterId: waiter.id,
          cashierId: cashier.id,
          orderedAt: orderTime,
          completedAt: status === OrderStatus.COMPLETED ? new Date() : null,
          items: {
            create: orderItems,
          },
        },
      });

      // Ödeme oluştur (eğer ödendiyse)
      if (paymentStatus === PaymentStatus.PAID) {
        await prisma.payment.create({
          data: {
            orderId: order.id,
            paymentMethodId: cashPaymentMethod.id,
            amount: totalAmount,
            status: PaymentStatus.PAID,
            paidAt: new Date(),
          },
        });
      }
    }

    console.log('✅ Test orders created');
  }

  console.log('🎉 Seeding completed successfully!');
  console.log('\n📋 Test Accounts:');
  console.log('Manager: manager1 / PIN: 123456');
  console.log('Cashier: cashier1 / PIN: 567856');
  console.log('Waiter: waiter1 / PIN: 999956');
  console.log('Kitchen: kitchen1 / PIN: 11116');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
