// Employees hook
import { useState, useEffect } from 'react';
import { Employee } from '../types/auth.types';
import { apiService } from '../services/api.service';
import toast from 'react-hot-toast';

export const useEmployees = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEmployees = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await apiService.getActiveEmployees();
      setEmployees(data);
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Çalışanlar yüklenemedi';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  return {
    employees,
    isLoading,
    error,
    refetch: fetchEmployees,
  };
};
