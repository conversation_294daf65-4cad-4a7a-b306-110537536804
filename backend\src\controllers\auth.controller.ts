// Auth controller - HTTP request handling
import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { PrismaClient } from '../generated/client';
import { PinLoginRequest } from '../middlewares/validation.middleware';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService(new PrismaClient());
  }

  /**
   * PIN ile giriş endpoint'i
   * POST /api/v1/auth/pin-login
   */
  pinLogin = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const loginRequest: PinLoginRequest = req.body;
      const deviceInfo = req.headers['user-agent'] || 'Unknown Device';

      const result = await this.authService.pinLogin(loginRequest, deviceInfo);

      res.status(200).json({
        success: true,
        message: '<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Çıkış endpoint'i
   * POST /api/v1/auth/logout
   */
  logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user?.sessionId) {
        res.status(400).json({
          success: false,
          message: 'Session bilgisi bulunamadı',
        });
        return;
      }

      await this.authService.logout(req.user.sessionId);

      res.status(200).json({
        success: true,
        message: 'Çıkış başarılı',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Token doğrulama endpoint'i
   * GET /api/v1/auth/verify
   */
  verifyToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Geçersiz token',
        });
        return;
      }

      // Session'ı doğrula
      const isValid = await this.authService.validateSession(req.user.sessionId);
      
      if (!isValid) {
        res.status(401).json({
          success: false,
          message: 'Session geçersiz',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Token geçerli',
        data: {
          userId: req.user.userId,
          role: req.user.role,
          branchId: req.user.branchId,
          sessionId: req.user.sessionId,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Kullanıcı profili endpoint'i
   * GET /api/v1/auth/profile
   */
  getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Kimlik doğrulama gereklidir',
        });
        return;
      }

      const prisma = new PrismaClient();
      const user = await prisma.user.findUnique({
        where: { id: req.user.userId },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
          role: true,
          branchId: true,
          lastLoginAt: true,
          branch: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
      });

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'Kullanıcı bulunamadı',
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: user,
      });
    } catch (error) {
      next(error);
    }
  };
}
