// Auth routes
import { Router } from 'express';
import rateLimit from 'express-rate-limit';
import { AuthController } from '../controllers/auth.controller';
import { ValidationMiddleware, PinLoginSchema } from '../middlewares/validation.middleware';
import { AuthMiddleware } from '../middlewares/auth.middleware';

const router = Router();
const authController = new AuthController();

// Rate limiting for PIN login
const pinLoginLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '600000'), // 10 dakika
  max: parseInt(process.env.RATE_LIMIT_MAX || '5'), // 5 deneme
  message: {
    error: 'Too Many Requests',
    message: 'Çok fazla hatalı deneme. 10 dakika sonra tekrar deneyin.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // IP + employeeId bazlı rate limiting
  keyGenerator: (req) => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const employeeId = req.body?.employeeId || 'unknown';
    return `${ip}:${employeeId}`;
  },
});

/**
 * @route POST /api/v1/auth/pin-login
 * @desc PIN ile giriş yapar
 * @access Public
 */
router.post(
  '/pin-login',
  pinLoginLimiter,
  ValidationMiddleware.validateBody(PinLoginSchema),
  authController.pinLogin
);

/**
 * @route POST /api/v1/auth/logout
 * @desc Çıkış yapar
 * @access Private
 */
router.post(
  '/logout',
  AuthMiddleware.authenticate,
  authController.logout
);

/**
 * @route GET /api/v1/auth/verify
 * @desc Token doğrulama
 * @access Private
 */
router.get(
  '/verify',
  AuthMiddleware.authenticate,
  authController.verifyToken
);

/**
 * @route GET /api/v1/auth/profile
 * @desc Kullanıcı profili
 * @access Private
 */
router.get(
  '/profile',
  AuthMiddleware.authenticate,
  authController.getProfile
);

export default router;
