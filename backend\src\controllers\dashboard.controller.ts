// Dashboard controller - HTTP request handling
import { Request, Response, NextFunction } from 'express';
import { DashboardService } from '../services/dashboard.service';
import { PrismaClient } from '../generated/client';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '../middlewares/error.middleware';

export class DashboardController {
  private dashboardService: DashboardService;

  constructor() {
    this.dashboardService = new DashboardService(new PrismaClient());
  }

  /**
   * Dashboard verilerini getir
   * GET /api/v1/dashboard
   */
  getDashboardData = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Authentication middleware'den gelen user bilgisi
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      const { branchId } = req.user;

      if (!branchId) {
        throw ErrorHandler.createError('Şube bilgisi bulunamadı', 400, 'BRANCH_ID_REQUIRED');
      }

      // Dashboard verilerini getir
      const dashboardData = await this.dashboardService.getDashboardData(branchId);

      res.status(200).json({
        success: true,
        message: 'Dashboard verileri başarıyla getirildi',
        data: dashboardData,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Sadece metrikleri getir
   * GET /api/v1/dashboard/metrics
   */
  getMetrics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      const { branchId } = req.user;

      if (!branchId) {
        throw ErrorHandler.createError('Şube bilgisi bulunamadı', 400, 'BRANCH_ID_REQUIRED');
      }

      const metrics = await this.dashboardService.getMetrics(branchId);

      res.status(200).json({
        success: true,
        message: 'Metrikler başarıyla getirildi',
        data: metrics,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Popüler ürünleri getir
   * GET /api/v1/dashboard/popular-products
   */
  getPopularProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      const { branchId } = req.user;
      const limit = parseInt(req.query.limit as string) || 5;

      if (!branchId) {
        throw ErrorHandler.createError('Şube bilgisi bulunamadı', 400, 'BRANCH_ID_REQUIRED');
      }

      const popularProducts = await this.dashboardService.getPopularProducts(branchId, limit);

      res.status(200).json({
        success: true,
        message: 'Popüler ürünler başarıyla getirildi',
        data: popularProducts,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Stok dışı ürünleri getir
   * GET /api/v1/dashboard/out-of-stock
   */
  getOutOfStockProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      const { branchId } = req.user;

      if (!branchId) {
        throw ErrorHandler.createError('Şube bilgisi bulunamadı', 400, 'BRANCH_ID_REQUIRED');
      }

      const outOfStockProducts = await this.dashboardService.getOutOfStockProducts(branchId);

      res.status(200).json({
        success: true,
        message: 'Stok dışı ürünler başarıyla getirildi',
        data: outOfStockProducts,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * İşlemdeki siparişleri getir
   * GET /api/v1/dashboard/orders-in-progress
   */
  getOrdersInProgress = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      const { branchId } = req.user;

      if (!branchId) {
        throw ErrorHandler.createError('Şube bilgisi bulunamadı', 400, 'BRANCH_ID_REQUIRED');
      }

      const ordersInProgress = await this.dashboardService.getOrdersInProgress(branchId);

      res.status(200).json({
        success: true,
        message: 'İşlemdeki siparişler başarıyla getirildi',
        data: ordersInProgress,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Ödeme bekleyen siparişleri getir
   * GET /api/v1/dashboard/orders-waiting-payment
   */
  getOrdersWaitingPayment = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw ErrorHandler.createError('Kimlik doğrulama gereklidir', 401, 'UNAUTHORIZED');
      }

      const { branchId } = req.user;

      if (!branchId) {
        throw ErrorHandler.createError('Şube bilgisi bulunamadı', 400, 'BRANCH_ID_REQUIRED');
      }

      const ordersWaitingPayment = await this.dashboardService.getOrdersWaitingPayment(branchId);

      res.status(200).json({
        success: true,
        message: 'Ödeme bekleyen siparişler başarıyla getirildi',
        data: ordersWaitingPayment,
      });
    } catch (error) {
      next(error);
    }
  };
}

// Singleton instance export
export const dashboardController = new DashboardController();
