// JWT utility fonksiyonları
import jwt, { SignOptions } from 'jsonwebtoken';
import { JwtPayload } from '../types/auth.types';

export class JwtUtils {
  private static readonly SECRET = process.env.JWT_SECRET || 'your-secret-key';
  private static readonly EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

  /**
   * JWT token oluşturur
   */
  static generateToken(payload: Omit<JwtPayload, 'iat' | 'exp'>): string {
    try {
      return jwt.sign(payload as object, this.SECRET, {
        expiresIn: this.EXPIRES_IN
      } as SignOptions);
    } catch (error) {
      throw new Error('Token oluşturma hatası');
    }
  }

  /**
   * JWT token'ı doğrular ve payload'ı döndürür
   */
  static verifyToken(token: string): JwtPayload {
    try {
      return jwt.verify(token, this.SECRET) as JwtPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token süresi dolmuş');
      }
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Geçersiz token');
      }
      throw new Error('Token doğrulama hatası');
    }
  }

  /**
   * Token'dan payload'ı decode eder (doğrulama yapmaz)
   */
  static decodeToken(token: string): JwtPayload | null {
    try {
      return jwt.decode(token) as JwtPayload;
    } catch {
      return null;
    }
  }

  /**
   * Token'ın geçerli olup olmadığını kontrol eder
   */
  static isTokenValid(token: string): boolean {
    try {
      this.verifyToken(token);
      return true;
    } catch {
      return false;
    }
  }
}
