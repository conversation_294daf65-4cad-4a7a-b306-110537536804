// Employee routes
import { Router } from 'express';
import { EmployeeController } from '../controllers/employee.controller';
import { AuthMiddleware } from '../middlewares/auth.middleware';
import { UserRole } from '../generated/client';

const router = Router();
const employeeController = new EmployeeController();

/**
 * @route GET /api/v1/employees/active-shift
 * @desc Aktif vardiyası olan çalışanları getirir
 * @access Public (Giriş ekranı için)
 */
router.get(
  '/active-shift',
  employeeController.getActiveShiftEmployees
);

/**
 * @route GET /api/v1/employees/active-sessions
 * @desc Aktif session'ları olan çalı<PERSON>anları getirir
 * @access Private (Manager+)
 */
router.get(
  '/active-sessions',
  AuthMiddleware.authenticate,
  AuthMiddleware.authorize([
    UserRole.SUPER_ADMIN,
    UserRole.ADMIN,
    UserRole.BRANCH_MANAGER,
  ]),
  employeeController.getActiveSessionEmployees
);

/**
 * @route GET /api/v1/employees/:id
 * @desc Çalışan detaylarını getirir
 * @access Private
 */
router.get(
  '/:id',
  AuthMiddleware.authenticate,
  employeeController.getEmployeeById
);

/**
 * @route GET /api/v1/employees/:id/pin-status
 * @desc Çalışanın PIN durumunu kontrol eder
 * @access Private (Manager+)
 */
router.get(
  '/:id/pin-status',
  AuthMiddleware.authenticate,
  AuthMiddleware.authorize([
    UserRole.SUPER_ADMIN,
    UserRole.ADMIN,
    UserRole.BRANCH_MANAGER,
  ]),
  employeeController.checkPinStatus
);

/**
 * @route GET /api/v1/employees/count/:branchId
 * @desc Şube bazlı çalışan sayısını getirir
 * @access Private (Manager+)
 */
router.get(
  '/count/:branchId',
  AuthMiddleware.authenticate,
  AuthMiddleware.authorize([
    UserRole.SUPER_ADMIN,
    UserRole.ADMIN,
    UserRole.BRANCH_MANAGER,
  ]),
  employeeController.getEmployeeCountByBranch
);

export default router;
