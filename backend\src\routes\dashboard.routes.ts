// Dashboard routes - API endpoint tanımları
import { Router } from 'express';
import rateLimit from 'express-rate-limit';
import { AuthMiddleware } from '../middlewares/auth.middleware';
import { dashboardController } from '../controllers/dashboard.controller';

const router = Router();

// Rate limiting - Dashboard verilerini çok sık çekmemek için
const dashboardLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 dakika
  max: 30, // Dakikada maksimum 30 istek
  message: {
    error: 'Rate Limit Exceeded',
    message: 'Dashboard verilerini çok sık talep ediyorsunuz. Lütfen bekleyin.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Tüm dashboard endpoint'leri için authentication gerekli
router.use(AuthMiddleware.authenticate);

/**
 * @route GET /api/v1/dashboard
 * @desc Tüm dashboard verilerini getirir (önerilen ana endpoint)
 * @access Private
 * @rateLimit 30 requests per minute
 */
router.get(
  '/',
  dashboardLimiter,
  dashboardController.getDashboardData
);

/**
 * @route GET /api/v1/dashboard/metrics
 * @desc Sadece dashboard metriklerini getirir
 * @access Private
 * @rateLimit 30 requests per minute
 */
router.get(
  '/metrics',
  dashboardLimiter,
  dashboardController.getMetrics
);

/**
 * @route GET /api/v1/dashboard/popular-products
 * @desc Popüler ürünleri getirir
 * @access Private
 * @query limit: number (optional, default: 5)
 * @rateLimit 30 requests per minute
 */
router.get(
  '/popular-products',
  dashboardLimiter,
  dashboardController.getPopularProducts
);

/**
 * @route GET /api/v1/dashboard/out-of-stock
 * @desc Stok dışı ürünleri getirir
 * @access Private
 * @rateLimit 30 requests per minute
 */
router.get(
  '/out-of-stock',
  dashboardLimiter,
  dashboardController.getOutOfStockProducts
);

/**
 * @route GET /api/v1/dashboard/orders-in-progress
 * @desc İşlemdeki siparişleri getirir
 * @access Private
 * @rateLimit 30 requests per minute
 */
router.get(
  '/orders-in-progress',
  dashboardLimiter,
  dashboardController.getOrdersInProgress
);

/**
 * @route GET /api/v1/dashboard/orders-waiting-payment
 * @desc Ödeme bekleyen siparişleri getirir
 * @access Private
 * @rateLimit 30 requests per minute
 */
router.get(
  '/orders-waiting-payment',
  dashboardLimiter,
  dashboardController.getOrdersWaitingPayment
);

export default router;
