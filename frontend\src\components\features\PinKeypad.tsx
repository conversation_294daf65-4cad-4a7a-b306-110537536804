// PIN keypad component
import React from 'react';

interface PinKeypadProps {
  pin: string;
  onPinChange: (pin: string) => void;
  maxLength?: number;
  disabled?: boolean;
}

export const PinKeypad: React.FC<PinKeypadProps> = ({
  pin,
  onPinChange,
  maxLength = 6,
  disabled = false,
}) => {
  const handleNumberClick = (number: string) => {
    if (disabled || pin.length >= maxLength) return;
    onPinChange(pin + number);
  };

  const handleBackspace = () => {
    if (disabled || pin.length === 0) return;
    onPinChange(pin.slice(0, -1));
  };

  const handleClear = () => {
    if (disabled) return;
    onPinChange('');
  };

  const numbers = [
    ['1', '2', '3'],
    ['4', '5', '6'],
    ['7', '8', '9'],
    ['', '0', '⌫'],
  ];

  return (
    <div className="w-full max-w-md">
      {/* PIN Display */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Enter your PIN to validate yourself
        </label>
        <div className="flex justify-center space-x-2 p-4 bg-gray-50 rounded-lg">
          {Array.from({ length: maxLength }).map((_, index) => (
            <div
              key={index}
              className={`
                w-4 h-4 rounded-full border-2 transition-colors
                ${index < pin.length 
                  ? 'bg-blue-600 border-blue-600' 
                  : 'bg-white border-gray-300'
                }
              `}
            />
          ))}
        </div>
      </div>

      {/* Keypad */}
      <div className="grid grid-cols-3 gap-3">
        {numbers.map((row, rowIndex) =>
          row.map((number, colIndex) => {
            if (number === '') {
              return <div key={`${rowIndex}-${colIndex}`} />; // Empty cell
            }

            const isBackspace = number === '⌫';
            const isDisabled = disabled || (number !== '⌫' && pin.length >= maxLength);

            return (
              <button
                key={`${rowIndex}-${colIndex}`}
                type="button"
                onClick={isBackspace ? handleBackspace : () => handleNumberClick(number)}
                disabled={isDisabled}
                className={`
                  h-16 rounded-lg font-semibold text-xl transition-all duration-150
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                  ${isBackspace
                    ? 'bg-gray-200 hover:bg-gray-300 text-gray-700 disabled:bg-gray-100'
                    : 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 disabled:bg-gray-50'
                  }
                  ${isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
                `}
              >
                {isBackspace ? (
                  <svg className="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 12l6.414 6.414a2 2 0 001.414.586H19a2 2 0 002-2V7a2 2 0 00-2-2h-8.172a2 2 0 00-1.414.586L3 12z" />
                  </svg>
                ) : (
                  number
                )}
              </button>
            );
          })
        )}
      </div>

      {/* Clear button */}
      {pin.length > 0 && (
        <button
          type="button"
          onClick={handleClear}
          disabled={disabled}
          className="w-full mt-4 py-2 text-sm text-gray-600 hover:text-gray-800 disabled:text-gray-400 disabled:cursor-not-allowed"
        >
          Temizle
        </button>
      )}
    </div>
  );
};
